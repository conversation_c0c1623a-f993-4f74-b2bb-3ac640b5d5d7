<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Hide Header & Modal Sipetarung</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-content {
            padding: 20px;
        }
        
        .method-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .method-title {
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .method-description {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .demo-btn.warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .screenshot {
            text-align: center;
            margin: 20px 0;
        }
        
        .screenshot img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 Demo Script Hide Header & Modal</h1>
            <p>Script untuk menyembunyikan header menu dan popup modal di website Sipetarung BPN Buleleng</p>
        </div>
        
        <div class="demo-content">
            <div class="alert alert-info">
                <strong>Target Website:</strong> https://sipetarung.bpnbuleleng.id/peta1<br>
                <strong>Masalah:</strong> Header menu dan popup modal "Isi Data Pengunjung" menghalangi tampilan peta
            </div>
            
            <div class="method-card">
                <div class="method-title">🎯 Metode 1: HTML + JavaScript (Recommended)</div>
                <div class="method-description">
                    Solusi utama menggunakan iframe dengan kontrol JavaScript untuk hide/show header dan modal.
                    Dilengkapi dengan multiple fallback methods untuk mengatasi CORS restrictions.
                </div>
                
                <ul class="features-list">
                    <li>Interface kontrol yang user-friendly</li>
                    <li>Toggle header dan modal dengan satu klik</li>
                    <li>Fullscreen mode untuk viewing optimal</li>
                    <li>Keyboard shortcuts (Ctrl+H, F5, F11)</li>
                    <li>Loading indicator dan error handling</li>
                    <li>Responsive design untuk mobile</li>
                </ul>
                
                <a href="index.html" class="demo-btn" target="_blank">🚀 Coba Metode 1</a>
                <a href="#" class="demo-btn secondary" onclick="showCode('method1')">📝 Lihat Code</a>
            </div>
            
            <div class="method-card">
                <div class="method-title">🔧 Metode 2: PHP Proxy Server</div>
                <div class="method-description">
                    Solusi server-side menggunakan PHP cURL untuk fetch content dan modify secara otomatis.
                    Bypass CORS restrictions dengan proxy server.
                </div>
                
                <ul class="features-list">
                    <li>Server-side content modification</li>
                    <li>Automatic header dan modal removal</li>
                    <li>URL rewriting untuk fix relative paths</li>
                    <li>Bypass CORS restrictions</li>
                    <li>Toggle antara iframe dan proxy mode</li>
                </ul>
                
                <a href="alternative.php" class="demo-btn" target="_blank">🚀 Coba Metode 2</a>
                <a href="#" class="demo-btn secondary" onclick="showCode('method2')">📝 Lihat Code</a>
            </div>
            
            <div class="method-card">
                <div class="method-title">⚡ Metode 3: Advanced Modal Killer</div>
                <div class="method-description">
                    Script JavaScript advanced yang secara agresif mendeteksi dan menghapus semua jenis modal dan popup.
                    Menggunakan MutationObserver dan continuous monitoring.
                </div>
                
                <ul class="features-list">
                    <li>Comprehensive modal detection</li>
                    <li>Real-time DOM monitoring</li>
                    <li>Multiple removal strategies</li>
                    <li>Prevent modal opening events</li>
                    <li>Support untuk berbagai framework modal</li>
                </ul>
                
                <a href="#" class="demo-btn warning" onclick="testModalKiller()">⚡ Test Modal Killer</a>
                <a href="#" class="demo-btn secondary" onclick="showCode('method3')">📝 Lihat Code</a>
            </div>
            
            <div class="alert alert-warning">
                <strong>Catatan Penting:</strong><br>
                • Script ini dibuat khusus untuk website Sipetarung BPN Buleleng<br>
                • Beberapa method mungkin tidak bekerja karena CORS policy<br>
                • Gunakan PHP proxy method jika JavaScript method tidak bekerja<br>
                • Pastikan server mendukung PHP dan cURL untuk method 2
            </div>
            
            <div class="method-card">
                <div class="method-title">📋 Cara Penggunaan</div>
                <div class="method-description">
                    <strong>Langkah 1:</strong> Upload semua file ke web server Anda<br>
                    <strong>Langkah 2:</strong> Akses index.html melalui browser<br>
                    <strong>Langkah 3:</strong> Klik tombol "Hide Header & Modal"<br>
                    <strong>Langkah 4:</strong> Gunakan "Force Hide Modal" jika masih ada popup<br>
                    <strong>Langkah 5:</strong> Aktifkan "Modal Killer" untuk monitoring otomatis
                </div>
                
                <div class="code-block">
# File Structure:
├── index.html          # Main interface
├── style.css           # Styling
├── script.js           # JavaScript logic
├── modal-killer.js     # Advanced modal remover
├── alternative.php     # PHP proxy solution
└── README.md          # Documentation
                </div>
            </div>
            
            <div class="method-card">
                <div class="method-title">🎮 Keyboard Shortcuts</div>
                <div class="method-description">
                    <strong>Ctrl + H:</strong> Toggle header visibility<br>
                    <strong>F5:</strong> Refresh iframe<br>
                    <strong>F11:</strong> Toggle fullscreen mode<br>
                    <strong>Esc:</strong> Exit fullscreen (browser default)
                </div>
            </div>
            
            <div class="screenshot">
                <p><strong>Preview Interface:</strong></p>
                <div style="border: 2px solid #ddd; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <div style="background: #333; color: white; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
                        <button style="background: #4CAF50; color: white; border: none; padding: 5px 10px; margin-right: 5px; border-radius: 3px;">Hide Header & Modal</button>
                        <button style="background: #4CAF50; color: white; border: none; padding: 5px 10px; margin-right: 5px; border-radius: 3px;">Refresh</button>
                        <button style="background: #4CAF50; color: white; border: none; padding: 5px 10px; margin-right: 5px; border-radius: 3px;">Fullscreen</button>
                        <button style="background: #ff9800; color: white; border: none; padding: 5px 10px; margin-right: 5px; border-radius: 3px;">Force Hide Modal</button>
                        <button style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 3px;">Start Modal Killer</button>
                    </div>
                    <div style="background: white; height: 200px; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                        [Website Sipetarung akan ditampilkan di sini tanpa header dan modal]
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showCode(method) {
            let code = '';
            let title = '';
            
            switch(method) {
                case 'method1':
                    title = 'HTML + JavaScript Method';
                    code = `// Main JavaScript logic
$('#toggleHeader').click(function() {
    headerHidden = !headerHidden;
    hideHeader();
    hideModalsAdvanced();
});

function hideModalsAdvanced() {
    const modalSelectors = [
        '.modal', '.popup', '.overlay', '.dialog',
        '[class*="pengunjung"]', '[class*="visitor"]'
    ];
    // ... implementation
}`;
                    break;
                    
                case 'method2':
                    title = 'PHP Proxy Method';
                    code = `<?php
// Fetch content from target URL
$content = fetchContent($targetUrl);

// Inject CSS to hide headers and modals
$hideCSS = '
<style>
    .modal, .popup, header, .navbar {
        display: none !important;
    }
</style>';

echo modifyContent($content);
?>`;
                    break;
                    
                case 'method3':
                    title = 'Advanced Modal Killer';
                    code = `class ModalKiller {
    killModals() {
        this.modalSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.display = 'none !important';
                el.remove();
            });
        });
    }
}`;
                    break;
            }
            
            alert(title + '\\n\\n' + code);
        }
        
        function testModalKiller() {
            // Create a test modal
            const modal = document.createElement('div');
            modal.className = 'modal test-modal';
            modal.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border: 2px solid #333; z-index: 9999;';
            modal.innerHTML = '<h3>Test Modal</h3><p>This modal should be removed by Modal Killer</p>';
            document.body.appendChild(modal);
            
            // Load and test modal killer
            if (typeof ModalKiller !== 'undefined') {
                const killer = new ModalKiller();
                killer.start();
                setTimeout(() => {
                    alert('Modal Killer test completed! Check if the test modal was removed.');
                }, 2000);
            } else {
                alert('Modal Killer script not loaded. Please check modal-killer.js file.');
            }
        }
    </script>
</body>
</html>
