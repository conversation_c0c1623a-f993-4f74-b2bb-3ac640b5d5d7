/* Reset dan base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow: hidden;
}

.container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Controls styling */
.controls {
    background: rgba(255, 255, 255, 0.95);
    padding: 10px 20px;
    display: flex;
    gap: 10px;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #45a049, #4CAF50);
}

.btn:active {
    transform: translateY(0);
}

/* Iframe container */
.iframe-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#mainFrame {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
    transition: all 0.3s ease;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Spinner animation */
.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: #666;
    font-size: 16px;
    font-weight: 500;
}

/* Fullscreen mode */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
}

.fullscreen-mode .controls {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 25px;
    padding: 5px 15px;
}

.fullscreen-mode .btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 12px;
    padding: 5px 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .controls {
        padding: 8px 15px;
        flex-wrap: wrap;
    }
    
    .btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* Custom scrollbar untuk webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation untuk smooth transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Status indicator */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
