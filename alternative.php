<?php
/**
 * Alternative PHP solution untuk hide header menggunakan proxy
 * File: alternative.php
 */

// Set headers untuk CORS dan caching
header('Content-Type: text/html; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// URL target
$targetUrl = 'https://sipetarung.bpnbuleleng.id/peta1';

// Fungsi untuk fetch content dari URL
function fetchContent($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $content = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return $content;
    }
    
    return false;
}

// Fungsi untuk modify HTML content
function modifyContent($html) {
    // Inject CSS untuk hide header
    $hideHeaderCSS = '
    <style id="hideHeaderStyle">
        /* Hide various header selectors */
        header, .header, #header, .navbar, .nav-bar, .navigation,
        .top-bar, .site-header, .main-header, .page-header,
        nav, .nav, #nav, .menu, .main-menu, .primary-menu,
        .header-container, .header-wrapper, .header-section,
        .masthead, .banner, .top-section,
        .header-top, .header-bottom, .site-branding,
        .logo-container, .government-header {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
        }

        /* Hide modal popups */
        .modal, .popup, .overlay, .dialog, .lightbox,
        .modal-backdrop, .modal-overlay, .popup-overlay,
        .swal2-container, .sweet-alert, .alert-modal,
        [class*="modal"], [class*="popup"], [class*="dialog"],
        [id*="modal"], [id*="popup"], [id*="dialog"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            z-index: -1 !important;
        }

        /* Hide specific elements for Sipetarung */
        .form-popup, .visitor-form, .data-form,
        [class*="pengunjung"], [class*="visitor"],
        .bootstrap-modal, .ui-dialog, .fancybox-overlay {
            display: none !important;
            visibility: hidden !important;
        }

        /* Remove backdrop/overlay effects */
        body.modal-open, body.popup-open {
            overflow: auto !important;
            padding-right: 0 !important;
        }

        /* Adjust body/main content */
        body, .main-content, .content, #content, .container {
            padding-top: 0 !important;
            margin-top: 0 !important;
        }

        /* Additional adjustments */
        .page-wrapper, .site-wrapper {
            padding-top: 0 !important;
        }

        /* Hide contact info bar */
        .contact-bar, .info-bar, .top-info {
            display: none !important;
        }
    </style>';
    
    // Inject JavaScript untuk dynamic header hiding
    $hideHeaderJS = '
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Function to hide headers and modals
            function hideHeadersAndModals() {
                // Header selectors
                const headerSelectors = [
                    "header", ".header", "#header", ".navbar", ".nav-bar",
                    ".navigation", ".top-bar", ".site-header", ".main-header",
                    ".page-header", "nav", ".nav", "#nav", ".menu", ".main-menu",
                    ".contact-bar", ".info-bar", ".top-info"
                ];

                // Modal selectors
                const modalSelectors = [
                    ".modal", ".popup", ".overlay", ".dialog", ".lightbox",
                    ".modal-backdrop", ".modal-overlay", ".popup-overlay",
                    ".swal2-container", ".sweet-alert", ".alert-modal",
                    ".form-popup", ".visitor-form", ".data-form",
                    "[class*=\"modal\"]", "[class*=\"popup\"]", "[class*=\"dialog\"]",
                    "[id*=\"modal\"]", "[id*=\"popup\"]", "[id*=\"dialog\"]",
                    "[class*=\"pengunjung\"]", "[class*=\"visitor\"]"
                ];

                // Hide headers
                headerSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.display = "none";
                            el.style.visibility = "hidden";
                            el.style.height = "0";
                            el.style.overflow = "hidden";
                        });
                    } catch(e) {
                        console.log("Error hiding header element:", selector, e);
                    }
                });

                // Hide modals
                modalSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.display = "none";
                            el.style.visibility = "hidden";
                            el.style.opacity = "0";
                            el.style.zIndex = "-1";
                            // Remove element completely
                            el.remove();
                        });
                    } catch(e) {
                        console.log("Error hiding modal element:", selector, e);
                    }
                });

                // Remove modal classes from body
                document.body.classList.remove("modal-open", "popup-open");
                document.body.style.overflow = "auto";
                document.body.style.paddingRight = "0";

                // Close jQuery modals if available
                if (typeof jQuery !== "undefined") {
                    try {
                        jQuery(".modal").modal("hide");
                        jQuery(".popup").hide();
                    } catch(e) {
                        console.log("Error closing jQuery modals:", e);
                    }
                }

                // Close Bootstrap modals if available
                if (typeof bootstrap !== "undefined") {
                    try {
                        const modals = document.querySelectorAll(".modal");
                        modals.forEach(modal => {
                            const bsModal = bootstrap.Modal.getInstance(modal);
                            if (bsModal) {
                                bsModal.hide();
                            }
                        });
                    } catch(e) {
                        console.log("Error closing Bootstrap modals:", e);
                    }
                }
            }
            
            // Hide headers and modals immediately
            hideHeadersAndModals();

            // Hide headers and modals after delays (for dynamic content)
            setTimeout(hideHeadersAndModals, 1000);
            setTimeout(hideHeadersAndModals, 3000);
            setTimeout(hideHeadersAndModals, 5000);
            
            // Observer for dynamic content
            const observer = new MutationObserver(function(mutations) {
                let shouldHide = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
                        shouldHide = true;
                    }
                });
                
                if (shouldHide) {
                    setTimeout(hideHeadersAndModals, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>';
    
    // Insert CSS and JS before closing head tag
    $html = str_replace('</head>', $hideHeaderCSS . $hideHeaderJS . '</head>', $html);
    
    // Fix relative URLs to absolute URLs
    $html = str_replace('href="/', 'href="https://sipetarung.bpnbuleleng.id/', $html);
    $html = str_replace('src="/', 'src="https://sipetarung.bpnbuleleng.id/', $html);
    $html = str_replace("href='/", "href='https://sipetarung.bpnbuleleng.id/", $html);
    $html = str_replace("src='/", "src='https://sipetarung.bpnbuleleng.id/", $html);
    
    return $html;
}

// Main execution
if (isset($_GET['proxy']) && $_GET['proxy'] === 'true') {
    // Proxy mode - fetch and modify content
    $content = fetchContent($targetUrl);
    
    if ($content !== false) {
        $modifiedContent = modifyContent($content);
        echo $modifiedContent;
    } else {
        echo '<h1>Error: Could not fetch content from target URL</h1>';
        echo '<p>URL: ' . htmlspecialchars($targetUrl) . '</p>';
    }
} else {
    // Default mode - show iframe with controls
    ?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sipetarung Peta - PHP Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            height: 100vh;
            overflow: hidden;
        }
        
        .controls {
            background: #333;
            color: white;
            padding: 10px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .iframe-container {
            height: calc(100vh - 50px);
            position: relative;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button class="btn" onclick="toggleMode()">Toggle Proxy Mode</button>
        <button class="btn" onclick="refreshFrame()">Refresh</button>
        <span id="status">Iframe Mode</span>
    </div>
    
    <div class="iframe-container">
        <iframe id="mainFrame" src="<?php echo $targetUrl; ?>"></iframe>
        <div id="loading" class="loading" style="display: none;">Loading...</div>
    </div>
    
    <script>
        let isProxyMode = false;
        
        function toggleMode() {
            isProxyMode = !isProxyMode;
            const iframe = document.getElementById('mainFrame');
            const status = document.getElementById('status');
            
            if (isProxyMode) {
                iframe.src = '<?php echo $_SERVER['PHP_SELF']; ?>?proxy=true';
                status.textContent = 'Proxy Mode (Header Hidden)';
            } else {
                iframe.src = '<?php echo $targetUrl; ?>';
                status.textContent = 'Iframe Mode';
            }
            
            showLoading();
        }
        
        function refreshFrame() {
            const iframe = document.getElementById('mainFrame');
            showLoading();
            iframe.src = iframe.src;
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // Handle iframe load
        document.getElementById('mainFrame').onload = function() {
            hideLoading();
        };
        
        // Show loading initially
        showLoading();
    </script>
</body>
</html>
    <?php
}
?>
