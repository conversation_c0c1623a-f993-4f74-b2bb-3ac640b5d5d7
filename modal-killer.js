/**
 * Modal Killer Script - Advanced Modal and Popup Remover
 * Specifically designed for Sipetarung website
 */

class ModalKiller {
    constructor() {
        this.isActive = false;
        this.observers = [];
        this.intervalId = null;
        
        // Comprehensive list of modal selectors
        this.modalSelectors = [
            // Generic modal selectors
            '.modal', '.popup', '.overlay', '.dialog', '.lightbox',
            '.modal-backdrop', '.modal-overlay', '.popup-overlay',
            '.modal-container', '.popup-container', '.dialog-container',
            
            // Bootstrap modals
            '.modal.show', '.modal.fade', '.modal-dialog',
            
            // jQuery UI
            '.ui-dialog', '.ui-dialog-overlay', '.ui-widget-overlay',
            
            // SweetAlert
            '.swal2-container', '.swal2-backdrop', '.sweet-alert',
            
            // Custom form popups
            '.form-popup', '.visitor-form', '.data-form', '.info-form',
            
            // Specific to Indonesian government sites
            '[class*="pengunjung"]', '[class*="visitor"]', '[class*="tamu"]',
            '[class*="formulir"]', '[class*="form"]',
            
            // ID-based selectors
            '#modal', '#popup', '#dialog', '#overlay',
            '[id*="modal"]', '[id*="popup"]', '[id*="dialog"]',
            
            // Attribute-based selectors
            '[role="dialog"]', '[role="alertdialog"]',
            '[data-toggle="modal"]', '[data-bs-toggle="modal"]',
            
            // Fancybox and similar
            '.fancybox-overlay', '.fancybox-container',
            '.colorbox', '#colorbox',
            
            // Custom patterns
            '[class*="Modal"]', '[class*="Popup"]', '[class*="Dialog"]'
        ];
        
        // Header selectors
        this.headerSelectors = [
            'header', '.header', '#header',
            '.navbar', '.nav-bar', '.navigation',
            '.top-bar', '.site-header', '.main-header', '.page-header',
            'nav', '.nav', '#nav',
            '.menu', '.main-menu', '.primary-menu',
            '.header-container', '.header-wrapper', '.header-section',
            '.masthead', '.banner', '.top-section',
            '.header-top', '.header-bottom', '.site-branding',
            '.logo-container', '.government-header',
            '.contact-bar', '.info-bar', '.top-info'
        ];
    }
    
    // Start the modal killer
    start() {
        this.isActive = true;
        console.log('Modal Killer activated');
        
        // Immediate cleanup
        this.killModals();
        this.hideHeaders();
        
        // Set up continuous monitoring
        this.startContinuousMonitoring();
        
        // Set up mutation observer
        this.setupMutationObserver();
        
        // Set up event listeners to prevent modal opening
        this.preventModalOpening();
    }
    
    // Stop the modal killer
    stop() {
        this.isActive = false;
        console.log('Modal Killer deactivated');
        
        // Clear interval
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        // Disconnect observers
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
    }
    
    // Kill all modals
    killModals() {
        if (!this.isActive) return;
        
        let removedCount = 0;
        
        this.modalSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    // Multiple removal strategies
                    el.style.display = 'none !important';
                    el.style.visibility = 'hidden !important';
                    el.style.opacity = '0 !important';
                    el.style.zIndex = '-9999 !important';
                    el.style.position = 'absolute !important';
                    el.style.left = '-9999px !important';
                    el.style.top = '-9999px !important';
                    
                    // Remove classes that might show the modal
                    el.classList.remove('show', 'in', 'open', 'active', 'visible');
                    el.classList.add('hidden', 'hide');
                    
                    // Set attributes to hide
                    el.setAttribute('aria-hidden', 'true');
                    el.setAttribute('hidden', 'true');
                    
                    // Remove from DOM completely
                    if (el.parentNode) {
                        el.parentNode.removeChild(el);
                        removedCount++;
                    }
                });
            } catch (e) {
                console.log('Error removing modal:', selector, e);
            }
        });
        
        // Clean up body classes and styles
        this.cleanupBodyStyles();
        
        // Close programmatically
        this.closeProgrammatically();
        
        if (removedCount > 0) {
            console.log(`Removed ${removedCount} modal elements`);
        }
    }
    
    // Hide headers
    hideHeaders() {
        if (!this.isActive) return;
        
        this.headerSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none !important';
                    el.style.visibility = 'hidden !important';
                    el.style.height = '0 !important';
                    el.style.overflow = 'hidden !important';
                });
            } catch (e) {
                console.log('Error hiding header:', selector, e);
            }
        });
    }
    
    // Clean up body styles
    cleanupBodyStyles() {
        const body = document.body;
        const html = document.documentElement;
        
        // Remove modal-related classes
        const modalClasses = ['modal-open', 'popup-open', 'dialog-open', 'overlay-open'];
        modalClasses.forEach(cls => {
            body.classList.remove(cls);
            html.classList.remove(cls);
        });
        
        // Reset styles
        body.style.overflow = 'auto';
        body.style.paddingRight = '0';
        body.style.marginRight = '0';
        html.style.overflow = 'auto';
        html.style.paddingRight = '0';
        html.style.marginRight = '0';
    }
    
    // Close modals programmatically
    closeProgrammatically() {
        // jQuery modals
        if (typeof jQuery !== 'undefined') {
            try {
                jQuery('.modal').modal('hide');
                jQuery('.popup').hide();
                jQuery('.dialog').dialog('close');
            } catch (e) {
                console.log('Error closing jQuery modals:', e);
            }
        }
        
        // Bootstrap 5 modals
        if (typeof bootstrap !== 'undefined') {
            try {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                });
            } catch (e) {
                console.log('Error closing Bootstrap modals:', e);
            }
        }
        
        // SweetAlert
        if (typeof Swal !== 'undefined') {
            try {
                Swal.close();
            } catch (e) {
                console.log('Error closing SweetAlert:', e);
            }
        }
        
        // Custom close functions
        const closeFunctions = ['closeModal', 'hideModal', 'closePopup', 'hidePopup'];
        closeFunctions.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
                try {
                    window[funcName]();
                } catch (e) {
                    console.log(`Error calling ${funcName}:`, e);
                }
            }
        });
    }
    
    // Start continuous monitoring
    startContinuousMonitoring() {
        this.intervalId = setInterval(() => {
            if (this.isActive) {
                this.killModals();
                this.hideHeaders();
            }
        }, 1000); // Check every second
    }
    
    // Setup mutation observer
    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            if (!this.isActive) return;
            
            let shouldClean = false;
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes are modals
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            const isModal = this.modalSelectors.some(selector => {
                                try {
                                    return node.matches && node.matches(selector);
                                } catch (e) {
                                    return false;
                                }
                            });
                            if (isModal) {
                                shouldClean = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldClean) {
                setTimeout(() => this.killModals(), 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'style', 'aria-hidden']
        });
        
        this.observers.push(observer);
    }
    
    // Prevent modal opening
    preventModalOpening() {
        // Intercept common modal opening events
        const events = ['click', 'mousedown', 'touchstart'];
        
        events.forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                if (!this.isActive) return;
                
                const target = e.target;
                
                // Check if clicked element might open a modal
                const modalTriggers = [
                    '[data-toggle="modal"]',
                    '[data-bs-toggle="modal"]',
                    '[data-target*="modal"]',
                    '[data-bs-target*="modal"]',
                    '.modal-trigger',
                    '.popup-trigger'
                ];
                
                const isModalTrigger = modalTriggers.some(selector => {
                    try {
                        return target.matches && target.matches(selector);
                    } catch (e) {
                        return false;
                    }
                });
                
                if (isModalTrigger) {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    
                    // Kill any modals that might have opened
                    setTimeout(() => this.killModals(), 50);
                    
                    console.log('Prevented modal opening');
                    return false;
                }
            }, true); // Use capture phase
        });
    }
}

// Create global instance
window.modalKiller = new ModalKiller();

// Auto-start when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Don't auto-start, let the main script control it
    });
} else {
    // DOM is already ready
    // Don't auto-start, let the main script control it
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalKiller;
}
