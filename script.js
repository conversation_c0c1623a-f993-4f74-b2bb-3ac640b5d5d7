$(document).ready(function() {
    let headerHidden = false;
    let isFullscreen = false;
    
    // Fungsi untuk menyembunyikan header menggunakan CSS injection
    function hideHeader() {
        const iframe = document.getElementById('mainFrame');
        
        try {
            // Tunggu iframe selesai loading
            iframe.onload = function() {
                try {
                    // Akses dokumen iframe (hanya bisa jika same-origin)
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // Buat style element untuk menyembunyikan header
                    let hideStyle = iframeDoc.getElementById('hideHeaderStyle');
                    if (!hideStyle) {
                        hideStyle = iframeDoc.createElement('style');
                        hideStyle.id = 'hideHeaderStyle';
                        iframeDoc.head.appendChild(hideStyle);
                    }
                    
                    if (headerHidden) {
                        // CSS untuk menyembunyikan berbagai kemungkinan selector header
                        hideStyle.textContent = `
                            /* Hide header elements */
                            header, .header, #header, .navbar, .nav-bar, .navigation,
                            .top-bar, .site-header, .main-header, .page-header,
                            nav, .nav, #nav, .menu, .main-menu, .primary-menu,
                            .header-container, .header-wrapper, .header-section,
                            .masthead, .banner, .top-section {
                                display: none !important;
                                visibility: hidden !important;
                                height: 0 !important;
                                overflow: hidden !important;
                            }

                            /* Hide modal popups */
                            .modal, .popup, .overlay, .dialog, .lightbox,
                            .modal-backdrop, .modal-overlay, .popup-overlay,
                            .swal2-container, .sweet-alert, .alert-modal,
                            [class*="modal"], [class*="popup"], [class*="dialog"],
                            [id*="modal"], [id*="popup"], [id*="dialog"] {
                                display: none !important;
                                visibility: hidden !important;
                                opacity: 0 !important;
                                z-index: -1 !important;
                            }

                            /* Hide specific elements for Sipetarung */
                            .form-popup, .visitor-form, .data-form,
                            [class*="pengunjung"], [class*="visitor"],
                            .bootstrap-modal, .ui-dialog, .fancybox-overlay {
                                display: none !important;
                                visibility: hidden !important;
                            }

                            /* Remove backdrop/overlay effects */
                            body.modal-open, body.popup-open {
                                overflow: auto !important;
                                padding-right: 0 !important;
                            }

                            /* Adjust body/main content */
                            body, .main-content, .content, #content, .container {
                                padding-top: 0 !important;
                                margin-top: 0 !important;
                            }

                            /* Specific untuk website pemerintah */
                            .header-top, .header-bottom, .site-branding,
                            .logo-container, .government-header {
                                display: none !important;
                            }

                            /* Hide contact info bar */
                            .contact-bar, .info-bar, .top-info {
                                display: none !important;
                            }
                        `;
                    } else {
                        hideStyle.textContent = '';
                    }
                } catch (e) {
                    console.log('Cannot access iframe content (CORS restriction)');
                    // Fallback: gunakan CSS pada iframe container
                    applyIframeCSSFallback();
                }
            };
        } catch (e) {
            console.log('Error accessing iframe:', e);
            applyIframeCSSFallback();
        }
    }
    
    // Fallback method menggunakan CSS pada container iframe
    function applyIframeCSSFallback() {
        const iframe = $('#mainFrame');
        
        if (headerHidden) {
            // Pindahkan iframe ke atas untuk menyembunyikan header
            iframe.css({
                'margin-top': '-200px', // Increased to hide more header content
                'height': 'calc(100% + 200px)'
            });

            // Add additional CSS to hide modals via iframe styling
            iframe.addClass('hide-modals');
        } else {
            iframe.css({
                'margin-top': '0',
                'height': '100%'
            });

            iframe.removeClass('hide-modals');
        }
    }
    
    // Event handler untuk toggle header
    $('#toggleHeader').click(function() {
        headerHidden = !headerHidden;
        $(this).text(headerHidden ? 'Show Header & Modal' : 'Hide Header & Modal');

        // Tambahkan visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        hideHeader();

        // Force hide modals immediately
        if (headerHidden) {
            setTimeout(() => {
                hideModalsAdvanced();
            }, 500);
        }

        // Update status
        updateStatus();
    });

    // Event handler untuk force hide modal
    $('#forceHideModal').click(function() {
        // Tambahkan visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        // Force hide modals multiple times
        hideModalsAdvanced();
        setTimeout(() => {
            hideModalsAdvanced();
        }, 500);
        setTimeout(() => {
            hideModalsAdvanced();
        }, 1000);

        console.log('Force hide modal executed');
    });

    // Event handler untuk start modal killer
    $('#startModalKiller').click(function() {
        const iframe = document.getElementById('mainFrame');

        // Tambahkan visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        try {
            if (iframe.contentWindow && iframe.contentWindow.modalKiller) {
                iframe.contentWindow.modalKiller.start();
                console.log('Modal Killer started in iframe');
            } else {
                // Inject modal killer into iframe
                const script = iframe.contentDocument.createElement('script');
                script.src = 'modal-killer.js';
                script.onload = function() {
                    if (iframe.contentWindow.modalKiller) {
                        iframe.contentWindow.modalKiller.start();
                        console.log('Modal Killer injected and started in iframe');
                    }
                };
                iframe.contentDocument.head.appendChild(script);
            }
        } catch (e) {
            console.log('Cannot inject Modal Killer into iframe (CORS):', e);
            // Fallback: use our own modal killer methods
            hideModalsAdvanced();
        }

        // Update button text
        $(this).text('Modal Killer Active');
        $(this).prop('disabled', true);
    });
    
    // Event handler untuk refresh iframe
    $('#refreshFrame').click(function() {
        const iframe = document.getElementById('mainFrame');
        showLoading();
        
        // Tambahkan timestamp untuk force refresh
        const currentSrc = iframe.src.split('?')[0];
        iframe.src = currentSrc + '?t=' + new Date().getTime();
        
        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });
    
    // Event handler untuk fullscreen
    $('#fullscreen').click(function() {
        isFullscreen = !isFullscreen;
        
        if (isFullscreen) {
            $('body').addClass('fullscreen-mode');
            $(this).text('Exit Fullscreen');
        } else {
            $('body').removeClass('fullscreen-mode');
            $(this).text('Fullscreen');
        }
        
        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });
    
    // Fungsi untuk menampilkan loading
    function showLoading() {
        $('#loadingOverlay').removeClass('hidden');
    }
    
    // Fungsi untuk menyembunyikan loading
    function hideLoading() {
        $('#loadingOverlay').addClass('hidden');
    }
    
    // Update status indicator
    function updateStatus() {
        const statusText = headerHidden ? 'Header Hidden' : 'Header Visible';
        console.log('Status:', statusText);
    }
    
    // Handle iframe load event
    $('#mainFrame').on('load', function() {
        hideLoading();

        // Auto-hide header dan modal setelah loading
        if (headerHidden) {
            setTimeout(() => {
                hideHeader();
                hideModalsAdvanced();
            }, 1000);

            // Additional attempts to hide modals
            setTimeout(() => {
                hideModalsAdvanced();
            }, 3000);

            setTimeout(() => {
                hideModalsAdvanced();
            }, 5000);
        }

        // Tambahkan fade-in effect
        $(this).addClass('fade-in');
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl + H untuk toggle header
        if (e.ctrlKey && e.keyCode === 72) {
            e.preventDefault();
            $('#toggleHeader').click();
        }
        
        // F11 untuk fullscreen
        if (e.keyCode === 122) {
            e.preventDefault();
            $('#fullscreen').click();
        }
        
        // F5 untuk refresh
        if (e.keyCode === 116) {
            e.preventDefault();
            $('#refreshFrame').click();
        }
    });
    
    // Auto-hide header saat pertama kali load (opsional)
    setTimeout(() => {
        // Uncomment baris berikut jika ingin auto-hide header
        // $('#toggleHeader').click();
    }, 2000);
    
    // Handle window resize
    $(window).resize(function() {
        if (headerHidden) {
            hideHeader();
        }
    });
    
    // Initial setup
    showLoading();
    updateStatus();
    
    // Error handling untuk iframe
    $('#mainFrame').on('error', function() {
        hideLoading();
        console.error('Failed to load iframe content');
    });
    
    // Tambahkan CSS untuk button active state dan modal hiding
    $('<style>').text(`
        .btn-active {
            transform: scale(0.95) !important;
            background: linear-gradient(45deg, #45a049, #4CAF50) !important;
        }

        /* Additional CSS for hiding modals in iframe */
        #mainFrame.hide-modals {
            filter: none !important;
        }

        /* Overlay to block modal interactions */
        .modal-blocker {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: 9999;
            pointer-events: none;
        }

        .modal-blocker.active {
            pointer-events: all;
        }
    `).appendTo('head');

    // Function to hide modals with advanced techniques
    function hideModalsAdvanced() {
        const iframe = document.getElementById('mainFrame');

        try {
            if (iframe.contentWindow && iframe.contentDocument) {
                const doc = iframe.contentDocument;

                // Hide modal elements
                const modalSelectors = [
                    '.modal', '.popup', '.overlay', '.dialog', '.lightbox',
                    '.modal-backdrop', '.modal-overlay', '.popup-overlay',
                    '.swal2-container', '.sweet-alert', '.alert-modal',
                    '.form-popup', '.visitor-form', '.data-form',
                    '[class*="modal"]', '[class*="popup"]', '[class*="dialog"]',
                    '[id*="modal"]', '[id*="popup"]', '[id*="dialog"]',
                    '[class*="pengunjung"]', '[class*="visitor"]'
                ];

                modalSelectors.forEach(selector => {
                    try {
                        const elements = doc.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.display = 'none';
                            el.style.visibility = 'hidden';
                            el.style.opacity = '0';
                            el.style.zIndex = '-1';
                            el.remove(); // Remove completely
                        });
                    } catch (e) {
                        console.log('Error hiding modal:', selector, e);
                    }
                });

                // Remove modal classes from body
                const body = doc.body;
                if (body) {
                    body.classList.remove('modal-open', 'popup-open');
                    body.style.overflow = 'auto';
                    body.style.paddingRight = '0';
                }

                // Close any open modals programmatically
                if (iframe.contentWindow.jQuery) {
                    try {
                        iframe.contentWindow.jQuery('.modal').modal('hide');
                        iframe.contentWindow.jQuery('.popup').hide();
                    } catch (e) {
                        console.log('Error closing jQuery modals:', e);
                    }
                }

                // Try to close Bootstrap modals
                if (iframe.contentWindow.bootstrap) {
                    try {
                        const modals = doc.querySelectorAll('.modal');
                        modals.forEach(modal => {
                            const bsModal = iframe.contentWindow.bootstrap.Modal.getInstance(modal);
                            if (bsModal) {
                                bsModal.hide();
                            }
                        });
                    } catch (e) {
                        console.log('Error closing Bootstrap modals:', e);
                    }
                }
            }
        } catch (e) {
            console.log('Cannot access iframe for modal hiding:', e);
        }
    }

    // Make function globally available
    window.hideModalsAdvanced = hideModalsAdvanced;
});

// Fungsi tambahan untuk advanced header hiding
function advancedHeaderHide() {
    const iframe = document.getElementById('mainFrame');
    
    // Method 1: Menggunakan MutationObserver untuk detect dynamic content
    if (iframe.contentWindow) {
        try {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // Re-apply header hiding jika ada perubahan DOM
                        setTimeout(() => {
                            hideHeader();
                        }, 100);
                    }
                });
            });
            
            observer.observe(iframe.contentDocument.body, {
                childList: true,
                subtree: true
            });
        } catch (e) {
            console.log('Cannot observe iframe mutations:', e);
        }
    }
}

// Utility function untuk detect header elements
function detectHeaderElements(doc) {
    const possibleHeaders = [
        'header', '.header', '#header', '.navbar', '.nav-bar',
        '.navigation', '.top-bar', '.site-header', '.main-header',
        '.page-header', 'nav', '.nav', '#nav', '.menu', '.main-menu'
    ];
    
    const foundHeaders = [];
    
    possibleHeaders.forEach(selector => {
        try {
            const elements = doc.querySelectorAll(selector);
            if (elements.length > 0) {
                foundHeaders.push({
                    selector: selector,
                    count: elements.length,
                    elements: elements
                });
            }
        } catch (e) {
            // Ignore invalid selectors
        }
    });
    
    return foundHeaders;
}
