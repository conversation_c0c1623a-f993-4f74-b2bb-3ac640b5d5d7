$(document).ready(function() {
    let headerHidden = false;
    let isFullscreen = false;
    let modalKillerActive = false;

    console.log('Script loaded successfully');

    // Fungsi untuk menyembunyikan header menggunakan CSS fallback
    function hideHeader() {
        const iframe = $('#mainFrame');
        console.log('hideHeader called, headerHidden:', headerHidden);

        if (headerHidden) {
            // Method 1: CSS margin adjustment (always works)
            iframe.css({
                'margin-top': '-200px',
                'height': 'calc(100% + 200px)',
                'transform': 'translateY(0)'
            });

            // Method 2: Try to inject CSS into iframe (may fail due to CORS)
            try {
                const iframeElement = document.getElementById('mainFrame');
                const iframeDoc = iframeElement.contentDocument || iframeElement.contentWindow.document;

                if (iframeDoc) {
                    let hideStyle = iframeDoc.getElementById('hideHeaderStyle');
                    if (!hideStyle) {
                        hideStyle = iframeDoc.createElement('style');
                        hideStyle.id = 'hideHeaderStyle';
                        iframeDoc.head.appendChild(hideStyle);
                    }
                    
                    hideStyle.textContent = `
                        /* Hide header elements */
                        header, .header, #header, .navbar, .nav-bar, .navigation,
                        .top-bar, .site-header, .main-header, .page-header,
                        nav, .nav, #nav, .menu, .main-menu, .primary-menu,
                        .header-container, .header-wrapper, .header-section,
                        .masthead, .banner, .top-section {
                            display: none !important;
                            visibility: hidden !important;
                            height: 0 !important;
                            overflow: hidden !important;
                        }

                        /* Hide modal popups */
                        .modal, .popup, .overlay, .dialog, .lightbox,
                        .modal-backdrop, .modal-overlay, .popup-overlay,
                        .swal2-container, .sweet-alert, .alert-modal,
                        [class*="modal"], [class*="popup"], [class*="dialog"],
                        [id*="modal"], [id*="popup"], [id*="dialog"] {
                            display: none !important;
                            visibility: hidden !important;
                            opacity: 0 !important;
                            z-index: -1 !important;
                        }

                        /* Hide specific elements for Sipetarung */
                        .form-popup, .visitor-form, .data-form,
                        [class*="pengunjung"], [class*="visitor"],
                        .bootstrap-modal, .ui-dialog, .fancybox-overlay {
                            display: none !important;
                            visibility: hidden !important;
                        }

                        /* Remove backdrop/overlay effects */
                        body.modal-open, body.popup-open {
                            overflow: auto !important;
                            padding-right: 0 !important;
                        }

                        /* Adjust body/main content */
                        body, .main-content, .content, #content, .container {
                            padding-top: 0 !important;
                            margin-top: 0 !important;
                        }

                        /* Hide contact info bar */
                        .contact-bar, .info-bar, .top-info {
                            display: none !important;
                        }
                    `;
                    console.log('CSS injected into iframe');
                }
            } catch (e) {
                console.log('Cannot access iframe content (CORS restriction):', e);
            }
        } else {
            // Reset iframe position
            iframe.css({
                'margin-top': '0',
                'height': '100%',
                'transform': 'translateY(0)'
            });
        }
    }
    
    // Simple function to force hide modals
    function forceHideModals() {
        const iframe = $('#mainFrame');

        // Method 1: Add overlay to block modals
        const overlay = $('<div>').css({
            'position': 'absolute',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background': 'transparent',
            'z-index': '9999',
            'pointer-events': 'none'
        });

        if ($('.modal-overlay-blocker').length === 0) {
            iframe.parent().append(overlay.addClass('modal-overlay-blocker'));
        }

        // Method 2: Try to access iframe and remove modals
        try {
            const iframeElement = document.getElementById('mainFrame');
            const iframeDoc = iframeElement.contentDocument || iframeElement.contentWindow.document;

            if (iframeDoc) {
                // Remove modal elements
                const modalSelectors = ['.modal', '.popup', '.overlay', '.dialog', '[class*="modal"]', '[id*="modal"]'];
                modalSelectors.forEach(selector => {
                    const elements = iframeDoc.querySelectorAll(selector);
                    elements.forEach(el => el.remove());
                });

                // Clean body classes
                iframeDoc.body.classList.remove('modal-open', 'popup-open');
                iframeDoc.body.style.overflow = 'auto';
                iframeDoc.body.style.paddingRight = '0';
            }
        } catch (e) {
            console.log('Cannot access iframe for modal removal:', e);
        }

        console.log('Force hide modals executed');
    }

    // Event handler untuk toggle header
    $('#toggleHeader').click(function() {
        console.log('Toggle header clicked');
        headerHidden = !headerHidden;
        $(this).text(headerHidden ? 'Show Header & Modal' : 'Hide Header & Modal');

        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        hideHeader();

        if (headerHidden) {
            setTimeout(forceHideModals, 500);
            setTimeout(forceHideModals, 2000);
        }

        console.log('Header hidden:', headerHidden);
    });

    // Event handler untuk force hide modal
    $('#forceHideModal').click(function() {
        console.log('Force hide modal clicked');

        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        // Execute multiple times
        forceHideModals();
        setTimeout(forceHideModals, 500);
        setTimeout(forceHideModals, 1000);
        setTimeout(forceHideModals, 2000);
    });

    // Event handler untuk start modal killer
    $('#startModalKiller').click(function() {
        const iframe = document.getElementById('mainFrame');

        // Tambahkan visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);

        try {
            if (iframe.contentWindow && iframe.contentWindow.modalKiller) {
                iframe.contentWindow.modalKiller.start();
                console.log('Modal Killer started in iframe');
            } else {
                // Inject modal killer into iframe
                const script = iframe.contentDocument.createElement('script');
                script.src = 'modal-killer.js';
                script.onload = function() {
                    if (iframe.contentWindow.modalKiller) {
                        iframe.contentWindow.modalKiller.start();
                        console.log('Modal Killer injected and started in iframe');
                    }
                };
                iframe.contentDocument.head.appendChild(script);
            }
        } catch (e) {
            console.log('Cannot inject Modal Killer into iframe (CORS):', e);
            // Fallback: use our own modal killer methods
            hideModalsAdvanced();
        }

        // Update button text
        $(this).text('Modal Killer Active');
        $(this).prop('disabled', true);
    });
    
    // Event handler untuk refresh iframe
    $('#refreshFrame').click(function() {
        console.log('Refresh clicked');
        const iframe = document.getElementById('mainFrame');
        showLoading();

        // Add timestamp untuk force refresh
        const currentSrc = iframe.src.split('?')[0];
        iframe.src = currentSrc + '?t=' + new Date().getTime();

        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });

    // Event handler untuk fullscreen
    $('#fullscreen').click(function() {
        console.log('Fullscreen clicked');
        isFullscreen = !isFullscreen;

        if (isFullscreen) {
            $('body').addClass('fullscreen-mode');
            $(this).text('Exit Fullscreen');
        } else {
            $('body').removeClass('fullscreen-mode');
            $(this).text('Fullscreen');
        }

        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });

    // Event handler untuk start modal killer
    $('#startModalKiller').click(function() {
        console.log('Modal Killer clicked');

        if (!modalKillerActive) {
            modalKillerActive = true;
            $(this).text('Modal Killer Active').addClass('btn-active');

            // Start continuous modal killing
            const killInterval = setInterval(() => {
                if (modalKillerActive) {
                    forceHideModals();
                } else {
                    clearInterval(killInterval);
                }
            }, 1000);

            console.log('Modal Killer started');
        } else {
            modalKillerActive = false;
            $(this).text('Start Modal Killer').removeClass('btn-active');
            console.log('Modal Killer stopped');
        }
    });
    
    // Fungsi untuk menampilkan loading
    function showLoading() {
        $('#loadingOverlay').removeClass('hidden');
    }
    
    // Fungsi untuk menyembunyikan loading
    function hideLoading() {
        $('#loadingOverlay').addClass('hidden');
    }
    

    
    // Handle iframe load event
    $('#mainFrame').on('load', function() {
        console.log('Iframe loaded');
        hideLoading();

        // Auto-apply hiding if already enabled
        if (headerHidden) {
            setTimeout(() => {
                hideHeader();
                forceHideModals();
            }, 1000);

            setTimeout(() => {
                forceHideModals();
            }, 3000);
        }

        // Add fade-in effect
        $(this).addClass('fade-in');
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl + H untuk toggle header
        if (e.ctrlKey && e.keyCode === 72) {
            e.preventDefault();
            $('#toggleHeader').click();
        }
        
        // F11 untuk fullscreen
        if (e.keyCode === 122) {
            e.preventDefault();
            $('#fullscreen').click();
        }
        
        // F5 untuk refresh
        if (e.keyCode === 116) {
            e.preventDefault();
            $('#refreshFrame').click();
        }
    });
    
    // Auto-hide header saat pertama kali load (opsional)
    setTimeout(() => {
        // Uncomment baris berikut jika ingin auto-hide header
        // $('#toggleHeader').click();
    }, 2000);
    
    // Handle window resize
    $(window).resize(function() {
        if (headerHidden) {
            hideHeader();
        }
    });

    // Initial setup
    showLoading();
    console.log('Initial setup completed');

    // Error handling untuk iframe
    $('#mainFrame').on('error', function() {
        hideLoading();
        console.error('Failed to load iframe content');
    });
    
    // Add CSS for button active state
    $('<style>').text(`
        .btn-active {
            transform: scale(0.95) !important;
            background: linear-gradient(45deg, #45a049, #4CAF50) !important;
        }
    `).appendTo('head');

    console.log('All event handlers attached successfully');
});
