$(document).ready(function() {
    let headerHidden = false;
    let isFullscreen = false;
    
    // Fungsi untuk menyembunyikan header menggunakan CSS injection
    function hideHeader() {
        const iframe = document.getElementById('mainFrame');
        
        try {
            // Tunggu iframe selesai loading
            iframe.onload = function() {
                try {
                    // Akses dokumen iframe (hanya bisa jika same-origin)
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // Buat style element untuk menyembunyikan header
                    let hideStyle = iframeDoc.getElementById('hideHeaderStyle');
                    if (!hideStyle) {
                        hideStyle = iframeDoc.createElement('style');
                        hideStyle.id = 'hideHeaderStyle';
                        iframeDoc.head.appendChild(hideStyle);
                    }
                    
                    if (headerHidden) {
                        // CSS untuk menyembunyikan berbagai kemungkinan selector header
                        hideStyle.textContent = `
                            header, .header, #header, .navbar, .nav-bar, .navigation,
                            .top-bar, .site-header, .main-header, .page-header,
                            nav, .nav, #nav, .menu, .main-menu, .primary-menu,
                            .header-container, .header-wrapper, .header-section,
                            .masthead, .banner, .top-section {
                                display: none !important;
                                visibility: hidden !important;
                                height: 0 !important;
                                overflow: hidden !important;
                            }
                            
                            /* Adjust body/main content */
                            body, .main-content, .content, #content, .container {
                                padding-top: 0 !important;
                                margin-top: 0 !important;
                            }
                            
                            /* Specific untuk website pemerintah */
                            .header-top, .header-bottom, .site-branding,
                            .logo-container, .government-header {
                                display: none !important;
                            }
                        `;
                    } else {
                        hideStyle.textContent = '';
                    }
                } catch (e) {
                    console.log('Cannot access iframe content (CORS restriction)');
                    // Fallback: gunakan CSS pada iframe container
                    applyIframeCSSFallback();
                }
            };
        } catch (e) {
            console.log('Error accessing iframe:', e);
            applyIframeCSSFallback();
        }
    }
    
    // Fallback method menggunakan CSS pada container iframe
    function applyIframeCSSFallback() {
        const iframe = $('#mainFrame');
        
        if (headerHidden) {
            // Pindahkan iframe ke atas untuk menyembunyikan header
            iframe.css({
                'margin-top': '-150px', // Sesuaikan nilai ini
                'height': 'calc(100% + 150px)'
            });
        } else {
            iframe.css({
                'margin-top': '0',
                'height': '100%'
            });
        }
    }
    
    // Event handler untuk toggle header
    $('#toggleHeader').click(function() {
        headerHidden = !headerHidden;
        $(this).text(headerHidden ? 'Show Header' : 'Hide Header');
        
        // Tambahkan visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
        
        hideHeader();
        
        // Update status
        updateStatus();
    });
    
    // Event handler untuk refresh iframe
    $('#refreshFrame').click(function() {
        const iframe = document.getElementById('mainFrame');
        showLoading();
        
        // Tambahkan timestamp untuk force refresh
        const currentSrc = iframe.src.split('?')[0];
        iframe.src = currentSrc + '?t=' + new Date().getTime();
        
        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });
    
    // Event handler untuk fullscreen
    $('#fullscreen').click(function() {
        isFullscreen = !isFullscreen;
        
        if (isFullscreen) {
            $('body').addClass('fullscreen-mode');
            $(this).text('Exit Fullscreen');
        } else {
            $('body').removeClass('fullscreen-mode');
            $(this).text('Fullscreen');
        }
        
        // Visual feedback
        $(this).addClass('btn-active');
        setTimeout(() => {
            $(this).removeClass('btn-active');
        }, 200);
    });
    
    // Fungsi untuk menampilkan loading
    function showLoading() {
        $('#loadingOverlay').removeClass('hidden');
    }
    
    // Fungsi untuk menyembunyikan loading
    function hideLoading() {
        $('#loadingOverlay').addClass('hidden');
    }
    
    // Update status indicator
    function updateStatus() {
        const statusText = headerHidden ? 'Header Hidden' : 'Header Visible';
        console.log('Status:', statusText);
    }
    
    // Handle iframe load event
    $('#mainFrame').on('load', function() {
        hideLoading();
        
        // Auto-hide header setelah loading jika diperlukan
        if (headerHidden) {
            setTimeout(() => {
                hideHeader();
            }, 1000);
        }
        
        // Tambahkan fade-in effect
        $(this).addClass('fade-in');
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl + H untuk toggle header
        if (e.ctrlKey && e.keyCode === 72) {
            e.preventDefault();
            $('#toggleHeader').click();
        }
        
        // F11 untuk fullscreen
        if (e.keyCode === 122) {
            e.preventDefault();
            $('#fullscreen').click();
        }
        
        // F5 untuk refresh
        if (e.keyCode === 116) {
            e.preventDefault();
            $('#refreshFrame').click();
        }
    });
    
    // Auto-hide header saat pertama kali load (opsional)
    setTimeout(() => {
        // Uncomment baris berikut jika ingin auto-hide header
        // $('#toggleHeader').click();
    }, 2000);
    
    // Handle window resize
    $(window).resize(function() {
        if (headerHidden) {
            hideHeader();
        }
    });
    
    // Initial setup
    showLoading();
    updateStatus();
    
    // Error handling untuk iframe
    $('#mainFrame').on('error', function() {
        hideLoading();
        console.error('Failed to load iframe content');
    });
    
    // Tambahkan CSS untuk button active state
    $('<style>').text(`
        .btn-active {
            transform: scale(0.95) !important;
            background: linear-gradient(45deg, #45a049, #4CAF50) !important;
        }
    `).appendTo('head');
});

// Fungsi tambahan untuk advanced header hiding
function advancedHeaderHide() {
    const iframe = document.getElementById('mainFrame');
    
    // Method 1: Menggunakan MutationObserver untuk detect dynamic content
    if (iframe.contentWindow) {
        try {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // Re-apply header hiding jika ada perubahan DOM
                        setTimeout(() => {
                            hideHeader();
                        }, 100);
                    }
                });
            });
            
            observer.observe(iframe.contentDocument.body, {
                childList: true,
                subtree: true
            });
        } catch (e) {
            console.log('Cannot observe iframe mutations:', e);
        }
    }
}

// Utility function untuk detect header elements
function detectHeaderElements(doc) {
    const possibleHeaders = [
        'header', '.header', '#header', '.navbar', '.nav-bar',
        '.navigation', '.top-bar', '.site-header', '.main-header',
        '.page-header', 'nav', '.nav', '#nav', '.menu', '.main-menu'
    ];
    
    const foundHeaders = [];
    
    possibleHeaders.forEach(selector => {
        try {
            const elements = doc.querySelectorAll(selector);
            if (elements.length > 0) {
                foundHeaders.push({
                    selector: selector,
                    count: elements.length,
                    elements: elements
                });
            }
        } catch (e) {
            // Ignore invalid selectors
        }
    });
    
    return foundHeaders;
}
