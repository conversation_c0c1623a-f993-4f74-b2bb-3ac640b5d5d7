# Hide Header Script untuk Sipetarung Peta

Script ini dibuat untuk menyembunyikan menu header di website https://sipetarung.bpnbuleleng.id/peta1 menggunakan iframe dan JavaScript.

## File yang Disediakan

### 1. **index.html** - <PERSON><PERSON><PERSON> (HTML + CSS + jQuery)
- Menggunakan iframe untuk menampilkan website target
- Kontrol untuk toggle header, refresh, dan fullscreen
- Responsive design dengan animasi smooth
- Keyboard shortcuts (Ctrl+H, F5, F11)

### 2. **style.css** - Styling
- Modern UI dengan gradient background
- Responsive design
- Loading animations
- Smooth transitions

### 3. **script.js** - JavaScript Logic
- jQuery untuk manipulasi DOM
- Multiple methods untuk hide header:
  - Direct iframe content manipulation (jika same-origin)
  - CSS fallback dengan margin adjustment
  - Advanced detection untuk dynamic content

### 4. **alternative.php** - Solusi PHP Proxy
- Server-side solution menggunakan cURL
- Proxy mode untuk bypass CORS restrictions
- Automatic header hiding dengan CSS injection
- URL rewriting untuk fix relative paths

## Cara Penggunaan

### Metode 1: HTML + JavaScript (Recommended)
1. Upload semua file ke web server
2. Akses `index.html` melalui browser
3. Klik tombol "Toggle Header" untuk hide/show header
4. Gunakan kontrol lainnya sesuai kebutuhan

### Metode 2: PHP Proxy
1. Upload `alternative.php` ke web server dengan PHP support
2. Akses file PHP melalui browser
3. Klik "Toggle Proxy Mode" untuk mengaktifkan header hiding
4. Mode proxy akan fetch content dan modify secara server-side

## Fitur

### Kontrol Interface
- **Toggle Header**: Hide/show header website target
- **Refresh**: Reload iframe content
- **Fullscreen**: Mode fullscreen untuk viewing optimal

### Keyboard Shortcuts
- `Ctrl + H`: Toggle header visibility
- `F5`: Refresh iframe
- `F11`: Toggle fullscreen mode

### Advanced Features
- Loading indicator dengan spinner animation
- Error handling untuk connection issues
- Responsive design untuk mobile devices
- Auto-detection untuk dynamic content
- Multiple fallback methods untuk cross-origin restrictions

## Technical Details

### Header Detection
Script akan mencari dan menyembunyikan elemen dengan selector:
```css
header, .header, #header, .navbar, .nav-bar, .navigation,
.top-bar, .site-header, .main-header, .page-header,
nav, .nav, #nav, .menu, .main-menu, .primary-menu,
.header-container, .header-wrapper, .header-section,
.masthead, .banner, .top-section
```

### CORS Handling
- Primary method: Direct iframe content access (same-origin)
- Fallback method: CSS margin adjustment
- PHP proxy method: Server-side content fetching

### Browser Compatibility
- Modern browsers dengan jQuery 3.6.0 support
- IE11+ (dengan polyfills)
- Mobile browsers (responsive design)

## Troubleshooting

### Jika Header Tidak Tersembunyi
1. Coba refresh iframe beberapa kali
2. Gunakan PHP proxy mode untuk bypass CORS
3. Check browser console untuk error messages
4. Pastikan website target dapat diakses

### Performance Issues
1. Gunakan loading indicator untuk UX yang lebih baik
2. Implement caching di PHP proxy jika diperlukan
3. Optimize CSS selectors untuk performa

## Customization

### Menambah Selector Header Baru
Edit file `script.js` dan `alternative.php`, tambahkan selector di array `possibleHeaders` atau CSS rules.

### Styling Customization
Edit `style.css` untuk mengubah appearance, colors, animations, dll.

### Functionality Extension
Tambahkan fitur baru di `script.js` seperti:
- Auto-hide timer
- Multiple website support
- User preferences saving

## Security Notes

- Script ini menggunakan iframe dan proxy, pastikan server aman
- Validate input jika menambahkan parameter user
- Consider rate limiting untuk PHP proxy
- HTTPS recommended untuk production use

## Dependencies

- jQuery 3.6.0 (loaded from CDN)
- Modern browser dengan CSS3 support
- PHP 7.0+ dengan cURL extension (untuk proxy mode)

## License

Free to use and modify untuk keperluan personal dan komersial.
